# برنامج حساب تسلسل الترقيات

## وصف البرنامج
برنامج لحساب تسلسل الترقيات الوظيفية بناءً على تاريخ أول مباشرة عمل ومدد المكوث المحددة لكل درجة.

## المميزات
- واجهة عربية سهلة الاستخدام
- إدخال تاريخ المباشرة من تقويم تفاعلي
- إعداد مدد الترقيات لكل درجة بشكل منفصل
- حساب تلقائي لجميع الترقيات
- تمييز الترقية القادمة بلون مختلف
- تصدير التقرير إلى ملف Excel
- حفظ الإعدادات تلقائياً

## متطلبات التشغيل
- Python 3.7 أو أحدث
- المكتبات المطلوبة (موجودة في requirements.txt)

## طريقة التثبيت والتشغيل

### الطريقة الأولى: تشغيل مباشر
1. تأكد من تثبيت Python على جهازك
2. شغل ملف `install_and_run.bat` لتثبيت المتطلبات وتشغيل البرنامج

### الطريقة الثانية: إنشاء نسخة محمولة (EXE)
1. شغل ملف `create_exe.bat` لإنشاء ملف EXE
2. ستجد الملف في مجلد `dist`
3. يمكنك نسخ الملف وتشغيله على أي جهاز بدون تثبيت Python

## طريقة الاستخدام

### 1. إدخال البيانات الأساسية
- أدخل اسم الموظف (اختياري)
- اختر تاريخ أول مباشرة عمل من التقويم

### 2. إعداد مدد الترقيات
- اضغط على زر "إعداد مدد الترقيات"
- حدد المدة بالسنوات والأشهر لكل درجة
- اضغط "حفظ وإغلاق" لحفظ الإعدادات

### 3. حساب الترقيات
- اضغط على زر "حساب الترقيات"
- ستظهر جميع الترقيات مع تواريخ الاستحقاق
- الترقيات المستحقة تظهر بلون أخضر
- الترقية القادمة تظهر بلون أصفر

### 4. تصدير التقرير
- اضغط على زر "تصدير Excel"
- اختر مكان الحفظ
- سيتم إنشاء ملف Excel منسق بالألوان

## الملفات المهمة
- `promotion_calculator.py`: الملف الرئيسي للبرنامج
- `promotion_settings.json`: ملف حفظ إعدادات مدد الترقيات
- `requirements.txt`: قائمة المكتبات المطلوبة

## ملاحظات
- يتم حفظ إعدادات مدد الترقيات تلقائياً
- البرنامج يدعم حساب الترقيات بدقة (سنوات وأشهر)
- يمكن تشغيل البرنامج على Windows بسهولة
- التقارير المصدرة تحتوي على تنسيق ملون وتفصيلي

## الدعم الفني
في حالة وجود أي مشاكل أو استفسارات، يرجى التواصل مع المطور.

---
تم تطوير البرنامج باستخدام Python و Tkinter
