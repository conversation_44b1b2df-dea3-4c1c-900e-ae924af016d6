🖥️ كيفية تشغيل برنامج حساب الترقيات على أي جهاز
===============================================

✅ نعم! يمكن تشغيل البرنامج على أي جهاز Windows بطرق متعددة:

🚀 الطريقة الأولى: النسخة المحمولة (EXE) - الأفضل
===================================================

الخطوات:
1️⃣ على جهازك الحالي: شغل create_exe.bat أو simple_build.bat
2️⃣ انتظر 5-10 دقائق حتى انتهاء العملية
3️⃣ ستجد ملف PromotionCalculator.exe في مجلد dist
4️⃣ انسخ هذا الملف إلى فلاشة USB أو أرسله عبر الإيميل
5️⃣ شغل الملف على أي جهاز Windows - يعمل فوراً!

المميزات:
✅ يعمل على أي جهاز Windows (7, 8, 10, 11) - 32 أو 64 بت
✅ لا يحتاج تثبيت Python أو أي برامج إضافية
✅ ملف واحد فقط - سهل النقل والمشاركة
✅ حجم الملف حوالي 50-100 ميجا
✅ يحفظ الإعدادات في نفس مجلد الملف

📁 الطريقة الثانية: نسخ المجلد كاملاً
====================================

إذا لم تتمكن من إنشاء EXE:
1️⃣ انسخ المجلد كاملاً إلى الجهاز الآخر
2️⃣ تأكد من وجود Python 3.7+ على الجهاز الآخر
3️⃣ شغل install_and_run.bat لتثبيت المكتبات وتشغيل البرنامج

المتطلبات للجهاز الآخر:
- Windows 7 أو أحدث
- Python 3.7 أو أحدث
- اتصال بالإنترنت (للمرة الأولى فقط)

🌐 الطريقة الثالثة: المشاركة عبر الإنترنت
=========================================

يمكنك:
1️⃣ رفع المجلد على Google Drive أو OneDrive
2️⃣ مشاركة الرابط مع الآخرين
3️⃣ تحميل وتشغيل على أي جهاز

📱 الطريقة الرابعة: إنشاء حزمة محمولة
====================================

لإنشاء حزمة كاملة للتوزيع:
1️⃣ انسخ ملف PromotionCalculator.exe
2️⃣ أضف ملفات التعليمات (تعليمات_سريعة.txt، مثال_عملي.txt)
3️⃣ ضع الكل في مجلد واحد
4️⃣ اضغط المجلد في ملف ZIP
5️⃣ شارك الملف المضغوط

🔧 استكشاف الأخطاء:
===================

إذا لم يعمل البرنامج على جهاز آخر:

❌ المشكلة: "لا يمكن تشغيل الملف"
✅ الحل: شغل كمدير (Run as Administrator)

❌ المشكلة: "برامج مكافحة الفيروسات تحجب الملف"
✅ الحل: أضف الملف لقائمة الاستثناءات

❌ المشكلة: "خطأ في تشغيل Python"
✅ الحل: استخدم النسخة المحمولة (EXE)

❌ المشكلة: "مكتبات مفقودة"
✅ الحل: شغل install_and_run.bat

💡 نصائح للتوزيع:
==================
✅ النسخة المحمولة (EXE) هي الأفضل للمستخدمين العاديين
✅ يمكن وضع الملف على فلاشة USB وتشغيله من أي جهاز
✅ البرنامج يحفظ إعداداته تلقائياً
✅ يمكن نسخ ملف promotion_settings.json لنقل الإعدادات
✅ البرنامج يدعم اللغة العربية بالكامل
✅ لا يحتاج اتصال بالإنترنت بعد التثبيت

🎯 الخلاصة:
============
البرنامج مصمم ليعمل على أي جهاز Windows بدون تعقيدات!
أفضل طريقة هي إنشاء ملف EXE ومشاركته مع الآخرين.
