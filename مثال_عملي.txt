مثال عملي على استخدام برنامج حساب تسلسل الترقيات
=====================================================

المثال الأول (حسب طلبك):
========================
- اسم الموظف: أحمد محمد
- تاريخ أول مباشرة: 01/01/1994
- الدرجة عند التعيين: الدرجة السادسة
- عدد العلاوات: 2 علاوة

التفسير الصحيح:
- الموظف بدأ العمل في 01/01/1994 في الدرجة السادسة
- مضت سنتان في الدرجة السادسة (2 علاوة)
- مدة الدرجة السادسة: 4 سنوات
- المدة المتبقية: 4 - 2 = 2 سنة
- تاريخ الترقية للسابعة: 01/01/1994 + 2 سنة = 01/01/1996 ✅

النتيجة المتوقعة:
الدرجة السادسة + 2 علاوة (الحالية) | 01/01/1994 | درجة حالية + 2 علاوة
الدرجة السابعة                      | 01/01/1996 | مستحقة (لأن 1996 مضى)
الدرجة الثامنة                      | 01/01/2000 | مستحقة
الدرجة التاسعة                      | 01/01/2004 | مستحقة
...

المثال الثاني:
==============
- اسم الموظف: فاطمة أحمد
- تاريخ أول مباشرة: 01/01/2020
- الدرجة عند التعيين: الدرجة الثالثة
- عدد العلاوات: 0 علاوة

التفسير:
- الموظفة بدأت العمل في 01/01/2020 في الدرجة الثالثة
- لم تمض أي سنوات في الدرجة الثالثة (0 علاوة)
- إذا كانت مدة الدرجة الثالثة 4 سنوات
- فتستحق الترقية للدرجة الرابعة في 01/01/2024

النتيجة المتوقعة:
الدرجة الثالثة (الحالية)    | 01/01/2020 | درجة حالية
الدرجة الرابعة              | 01/01/2024 | قادمة
الدرجة الخامسة              | 01/01/2028 | مستقبلية
...

المثال الثالث:
==============
- اسم الموظف: محمد علي
- تاريخ أول مباشرة: 01/06/2015
- الدرجة عند التعيين: مدير
- عدد العلاوات: 3 علاوة

التفسير:
- الموظف بدأ العمل في 01/06/2015 في درجة مدير
- مضت 3 سنوات في درجة مدير (3 علاوة)
- إذا كانت مدة درجة مدير 5 سنوات
- فيتبقى سنتان للترقية لدرجة مدير عام
- تاريخ الترقية القادمة: 01/06/2020

ملاحظات مهمة:
===============
1. العلاوة = سنة واحدة فقط
2. إذا كان عدد العلاوات = مدة الدرجة، فالموظف مستحق للترقية فوراً
3. إذا كان عدد العلاوات > مدة الدرجة، فالموظف متأخر في الترقية
4. البرنامج يحسب المدة المتبقية تلقائياً
