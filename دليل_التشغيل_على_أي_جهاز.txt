دليل تشغيل برنامج حساب الترقيات على أي جهاز
===========================================

🖥️ الطريقة الأولى: النسخة المحمولة (EXE) - الأسهل
====================================================

الخطوات:
1. شغل ملف create_exe.bat على جهازك الحالي
2. انتظر حتى انتهاء العملية (قد تستغرق 5-10 دقائق)
3. ستجد ملف PromotionCalculator.exe في مجلد dist
4. انسخ هذا الملف إلى أي جهاز Windows
5. شغل الملف مباشرة - لا يحتاج تثبيت أي شيء!

المميزات:
✅ يعمل على أي جهاز Windows (7, 8, 10, 11)
✅ لا يحتاج تثبيت Python أو أي مكتبات
✅ ملف واحد فقط - سهل النقل
✅ حجم الملف حوالي 50-100 ميجا

📁 الطريقة الثانية: نسخ المجلد كاملاً
=====================================

إذا لم تتمكن من إنشاء EXE، يمكنك:

1. انسخ المجلد كاملاً إلى الجهاز الآخر
2. تأكد من وجود Python 3.7+ على الجهاز الآخر
3. شغل install_and_run.bat لتثبيت المكتبات وتشغيل البرنامج

المتطلبات للجهاز الآخر:
- Windows 7 أو أحدث
- Python 3.7 أو أحدث (إذا لم تستخدم EXE)
- اتصال بالإنترنت (لتثبيت المكتبات في المرة الأولى فقط)

🌐 الطريقة الثالثة: رفع على GitHub أو Google Drive
=================================================

يمكنك:
1. رفع المجلد على GitHub أو Google Drive
2. تحميله على أي جهاز
3. تشغيله باستخدام الطرق المذكورة أعلاه

📋 الملفات المطلوبة للتشغيل:
=============================

للنسخة المحمولة (EXE):
- PromotionCalculator.exe فقط

للنسخة العادية:
- promotion_calculator.py
- requirements.txt
- install_and_run.bat أو run.bat
- promotion_settings.json (ينشأ تلقائياً)

🔧 استكشاف الأخطاء:
===================

إذا لم يعمل البرنامج على جهاز آخر:

1. تأكد من أن الجهاز يدعم Windows
2. شغل البرنامج كمدير (Run as Administrator)
3. تأكد من عدم حجب برامج مكافحة الفيروسات للملف
4. في حالة النسخة العادية، تأكد من تثبيت Python

💡 نصائح:
==========
- النسخة المحمولة (EXE) هي الأفضل للتوزيع
- يمكن وضع الملف على فلاشة USB وتشغيله من أي جهاز
- البرنامج يحفظ إعداداته في نفس مجلد الملف التنفيذي
- يمكن نسخ ملف promotion_settings.json لنقل الإعدادات بين الأجهزة
