#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار صحة حساب الترقيات
"""

from datetime import datetime
from dateutil.relativedelta import relativedelta

def test_promotion_calculation():
    """اختبار حساب الترقيات حسب المثال المطلوب"""
    
    # بيانات الاختبار
    start_date = datetime(1994, 1, 1).date()
    starting_degree = "الدرجة السادسة"
    allowances = 2  # علاوتان = سنتان مقضيتان
    degree_period = 4  # مدة الدرجة السادسة = 4 سنوات
    
    print("=== اختبار حساب الترقيات ===")
    print(f"تاريخ المباشرة: {start_date.strftime('%d/%m/%Y')}")
    print(f"الدرجة عند التعيين: {starting_degree}")
    print(f"عدد العلاوات: {allowances}")
    print(f"مدة الدرجة: {degree_period} سنوات")
    print()
    
    # الحساب الصحيح حسب طلبك:
    # المدة المتبقية = مدة الدرجة - العلاوات المقضية
    remaining_years = degree_period - allowances

    # تاريخ الترقية للدرجة السابعة = تاريخ المباشرة + المدة المتبقية
    promotion_to_7th = start_date + relativedelta(years=remaining_years)

    print("=== النتيجة المتوقعة (الحساب الصحيح) ===")
    print(f"الدرجة السادسة + {allowances} علاوة (الحالية): {start_date.strftime('%d/%m/%Y')}")
    print(f"مدة الدرجة السادسة: {degree_period} سنوات")
    print(f"العلاوات المقضية: {allowances} سنة")
    print(f"المدة المتبقية: {remaining_years} سنة")
    print(f"الدرجة السابعة: {promotion_to_7th.strftime('%d/%m/%Y')}")

    # حساب الترقيات التالية
    current_date = promotion_to_7th
    degrees = ["الدرجة الثامنة", "الدرجة التاسعة", "الدرجة العاشرة"]

    for degree in degrees:
        current_date = current_date + relativedelta(years=4)
        print(f"{degree}: {current_date.strftime('%d/%m/%Y')}")

    print()
    print("=== التحقق ===")
    print(f"هل الترقية للسابعة في 1996؟ {promotion_to_7th.year == 1996}")
    print(f"الحساب: 1994 + {remaining_years} سنة = {promotion_to_7th.year}")
    print("✅ هذا هو الحساب الصحيح!")

if __name__ == "__main__":
    test_promotion_calculation()
