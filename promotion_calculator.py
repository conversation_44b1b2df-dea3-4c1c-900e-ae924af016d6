#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج حساب تسلسل الترقيات
Promotion Sequence Calculator
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from tkcalendar import DateEntry
import json
import os
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment
import sys

class PromotionCalculator:
    def __init__(self, root):
        self.root = root
        self.root.title("برنامج حساب تسلسل الترقيات")
        self.root.geometry("800x600")
        self.root.configure(bg='#f0f0f0')
        
        # تحديد مسار ملف الإعدادات
        if getattr(sys, 'frozen', False):
            # إذا كان البرنامج مجمد (exe)
            self.app_dir = os.path.dirname(sys.executable)
        else:
            # إذا كان البرنامج يعمل من Python
            self.app_dir = os.path.dirname(os.path.abspath(__file__))
        
        self.settings_file = os.path.join(self.app_dir, "promotion_settings.json")
        
        # بيانات افتراضية للترقيات
        self.promotion_periods = {
            "الدرجة الأولى": {"years": 4, "months": 0},
            "الدرجة الثانية": {"years": 4, "months": 0},
            "الدرجة الثالثة": {"years": 4, "months": 0},
            "الدرجة الرابعة": {"years": 4, "months": 0},
            "الدرجة الخامسة": {"years": 4, "months": 0},
            "الدرجة السادسة": {"years": 4, "months": 0},
            "الدرجة السابعة": {"years": 4, "months": 0},
            "الدرجة الثامنة": {"years": 4, "months": 0},
            "الدرجة التاسعة": {"years": 4, "months": 0},
            "الدرجة العاشرة": {"years": 4, "months": 0}
        }
        
        self.load_settings()
        self.create_widgets()
        
    def load_settings(self):
        """تحميل إعدادات مدد الترقيات من الملف"""
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    self.promotion_periods = json.load(f)
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل الإعدادات: {str(e)}")
    
    def save_settings(self):
        """حفظ إعدادات مدد الترقيات في الملف"""
        try:
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.promotion_periods, f, ensure_ascii=False, indent=2)
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ الإعدادات: {str(e)}")
    
    def create_widgets(self):
        """إنشاء واجهة المستخدم"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # إعداد الشبكة
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # عنوان البرنامج
        title_label = ttk.Label(main_frame, text="برنامج حساب تسلسل الترقيات", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # إدخال اسم الموظف
        ttk.Label(main_frame, text="اسم الموظف:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.employee_name = ttk.Entry(main_frame, width=30)
        self.employee_name.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 0))
        
        # إدخال تاريخ المباشرة
        ttk.Label(main_frame, text="تاريخ أول مباشرة:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.start_date = DateEntry(main_frame, width=12, background='darkblue',
                                   foreground='white', borderwidth=2, 
                                   date_pattern='dd/mm/yyyy')
        self.start_date.grid(row=2, column=1, sticky=tk.W, pady=5, padx=(5, 0))
        
        # أزرار التحكم
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=3, pady=20)
        
        ttk.Button(button_frame, text="إعداد مدد الترقيات", 
                  command=self.open_settings_window).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="حساب الترقيات", 
                  command=self.calculate_promotions).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="تصدير Excel", 
                  command=self.export_to_excel).pack(side=tk.LEFT, padx=5)
        
        # جدول النتائج
        self.create_results_table(main_frame)
        
    def create_results_table(self, parent):
        """إنشاء جدول عرض النتائج"""
        # إطار الجدول
        table_frame = ttk.LabelFrame(parent, text="نتائج حساب الترقيات", padding="10")
        table_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        table_frame.columnconfigure(0, weight=1)
        table_frame.rowconfigure(0, weight=1)
        
        # إنشاء Treeview
        columns = ('degree', 'promotion_date', 'status')
        self.results_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # تحديد عناوين الأعمدة
        self.results_tree.heading('degree', text='الدرجة')
        self.results_tree.heading('promotion_date', text='تاريخ الاستحقاق')
        self.results_tree.heading('status', text='الحالة')
        
        # تحديد عرض الأعمدة
        self.results_tree.column('degree', width=150, anchor='center')
        self.results_tree.column('promotion_date', width=150, anchor='center')
        self.results_tree.column('status', width=100, anchor='center')
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.results_tree.yview)
        self.results_tree.configure(yscrollcommand=scrollbar.set)
        
        # وضع الجدول وشريط التمرير
        self.results_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # متغير لحفظ النتائج
        self.promotion_results = []
    
    def open_settings_window(self):
        """فتح نافذة إعداد مدد الترقيات"""
        settings_window = tk.Toplevel(self.root)
        settings_window.title("إعداد مدد الترقيات")
        settings_window.geometry("500x400")
        settings_window.configure(bg='#f0f0f0')
        
        # إطار رئيسي
        main_frame = ttk.Frame(settings_window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # عنوان
        ttk.Label(main_frame, text="تحديد مدد المكوث لكل درجة", 
                 font=('Arial', 12, 'bold')).pack(pady=(0, 10))
        
        # إطار الجدول
        table_frame = ttk.Frame(main_frame)
        table_frame.pack(fill=tk.BOTH, expand=True)
        
        # إنشاء جدول الإعدادات
        columns = ('degree', 'years', 'months')
        settings_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=10)
        
        settings_tree.heading('degree', text='الدرجة')
        settings_tree.heading('years', text='السنوات')
        settings_tree.heading('months', text='الأشهر')
        
        settings_tree.column('degree', width=200, anchor='center')
        settings_tree.column('years', width=100, anchor='center')
        settings_tree.column('months', width=100, anchor='center')
        
        # ملء الجدول بالبيانات الحالية
        for degree, period in self.promotion_periods.items():
            settings_tree.insert('', tk.END, values=(degree, period['years'], period['months']))
        
        settings_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # شريط التمرير
        scrollbar_settings = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=settings_tree.yview)
        settings_tree.configure(yscrollcommand=scrollbar_settings.set)
        scrollbar_settings.pack(side=tk.RIGHT, fill=tk.Y)
        
        # إطار التحرير
        edit_frame = ttk.LabelFrame(main_frame, text="تحرير المدة", padding="10")
        edit_frame.pack(fill=tk.X, pady=10)
        
        ttk.Label(edit_frame, text="السنوات:").grid(row=0, column=0, padx=5)
        years_var = tk.StringVar()
        years_entry = ttk.Entry(edit_frame, textvariable=years_var, width=10)
        years_entry.grid(row=0, column=1, padx=5)
        
        ttk.Label(edit_frame, text="الأشهر:").grid(row=0, column=2, padx=5)
        months_var = tk.StringVar()
        months_entry = ttk.Entry(edit_frame, textvariable=months_var, width=10)
        months_entry.grid(row=0, column=3, padx=5)
        
        def on_select(event):
            selection = settings_tree.selection()
            if selection:
                item = settings_tree.item(selection[0])
                degree = item['values'][0]
                years_var.set(str(self.promotion_periods[degree]['years']))
                months_var.set(str(self.promotion_periods[degree]['months']))
        
        settings_tree.bind('<<TreeviewSelect>>', on_select)
        
        def update_period():
            selection = settings_tree.selection()
            if selection:
                try:
                    years = int(years_var.get()) if years_var.get() else 0
                    months = int(months_var.get()) if months_var.get() else 0
                    
                    item = settings_tree.item(selection[0])
                    degree = item['values'][0]
                    
                    self.promotion_periods[degree]['years'] = years
                    self.promotion_periods[degree]['months'] = months
                    
                    # تحديث الجدول
                    settings_tree.item(selection[0], values=(degree, years, months))
                    
                    messagebox.showinfo("تم", "تم تحديث المدة بنجاح")
                except ValueError:
                    messagebox.showerror("خطأ", "يرجى إدخال أرقام صحيحة")
            else:
                messagebox.showwarning("تحذير", "يرجى اختيار درجة من الجدول")
        
        ttk.Button(edit_frame, text="تحديث", command=update_period).grid(row=0, column=4, padx=10)
        
        # أزرار الحفظ والإلغاء
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=10)
        
        def save_and_close():
            self.save_settings()
            settings_window.destroy()
            messagebox.showinfo("تم", "تم حفظ الإعدادات بنجاح")
        
        ttk.Button(button_frame, text="حفظ وإغلاق", command=save_and_close).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="إلغاء", command=settings_window.destroy).pack(side=tk.LEFT, padx=5)

    def calculate_promotions(self):
        """حساب تسلسل الترقيات"""
        try:
            # التحقق من صحة البيانات
            start_date = self.start_date.get_date()
            employee_name = self.employee_name.get().strip()

            if not employee_name:
                employee_name = "غير محدد"

            # مسح النتائج السابقة
            for item in self.results_tree.get_children():
                self.results_tree.delete(item)

            self.promotion_results = []
            current_date = start_date
            today = datetime.now().date()

            # حساب تواريخ الترقيات
            for degree, period in self.promotion_periods.items():
                # إضافة المدة للتاريخ الحالي
                promotion_date = current_date + relativedelta(years=period['years'], months=period['months'])

                # تحديد حالة الترقية
                if promotion_date <= today:
                    status = "مستحقة"
                    tag = "completed"
                elif promotion_date > today:
                    # أول ترقية قادمة
                    if not any(result['status'] == 'قادمة' for result in self.promotion_results):
                        status = "قادمة"
                        tag = "upcoming"
                    else:
                        status = "مستقبلية"
                        tag = "future"
                else:
                    status = "مستقبلية"
                    tag = "future"

                # إضافة النتيجة
                result = {
                    'degree': degree,
                    'promotion_date': promotion_date.strftime('%d/%m/%Y'),
                    'status': status,
                    'date_obj': promotion_date
                }
                self.promotion_results.append(result)

                # إضافة للجدول
                item_id = self.results_tree.insert('', tk.END,
                                                  values=(degree, promotion_date.strftime('%d/%m/%Y'), status),
                                                  tags=(tag,))

                # تحديث التاريخ الحالي للترقية التالية
                current_date = promotion_date

            # تنسيق الألوان
            self.results_tree.tag_configure('completed', background='#d4edda')
            self.results_tree.tag_configure('upcoming', background='#fff3cd', foreground='#856404')
            self.results_tree.tag_configure('future', background='#f8f9fa')

            # عرض رسالة النجاح
            next_promotion = next((r for r in self.promotion_results if r['status'] == 'قادمة'), None)
            if next_promotion:
                messagebox.showinfo("تم الحساب",
                                  f"تم حساب الترقيات بنجاح\nالترقية القادمة: {next_promotion['degree']} في {next_promotion['promotion_date']}")
            else:
                messagebox.showinfo("تم الحساب", "تم حساب الترقيات بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حساب الترقيات: {str(e)}")

    def export_to_excel(self):
        """تصدير النتائج إلى ملف Excel"""
        if not self.promotion_results:
            messagebox.showwarning("تحذير", "لا توجد نتائج للتصدير. يرجى حساب الترقيات أولاً")
            return

        try:
            # اختيار مكان الحفظ
            filename = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                title="حفظ تقرير الترقيات"
            )

            if not filename:
                return

            # إنشاء ملف Excel
            wb = Workbook()
            ws = wb.active
            ws.title = "تقرير الترقيات"

            # معلومات الموظف
            employee_name = self.employee_name.get().strip() or "غير محدد"
            start_date = self.start_date.get_date().strftime('%d/%m/%Y')

            ws['A1'] = "تقرير تسلسل الترقيات"
            ws['A1'].font = Font(size=16, bold=True)
            ws['A2'] = f"اسم الموظف: {employee_name}"
            ws['A3'] = f"تاريخ أول مباشرة: {start_date}"
            ws['A4'] = f"تاريخ إعداد التقرير: {datetime.now().strftime('%d/%m/%Y %H:%M')}"

            # عناوين الجدول
            headers = ['الدرجة', 'تاريخ الاستحقاق', 'الحالة']
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=6, column=col, value=header)
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
                cell.alignment = Alignment(horizontal="center")

            # بيانات الترقيات
            for row, result in enumerate(self.promotion_results, 7):
                ws.cell(row=row, column=1, value=result['degree'])
                ws.cell(row=row, column=2, value=result['promotion_date'])
                ws.cell(row=row, column=3, value=result['status'])

                # تلوين الصفوف حسب الحالة
                if result['status'] == 'قادمة':
                    for col in range(1, 4):
                        ws.cell(row=row, column=col).fill = PatternFill(
                            start_color="FFF3CD", end_color="FFF3CD", fill_type="solid")
                elif result['status'] == 'مستحقة':
                    for col in range(1, 4):
                        ws.cell(row=row, column=col).fill = PatternFill(
                            start_color="D4EDDA", end_color="D4EDDA", fill_type="solid")

            # تنسيق العرض
            for col in range(1, 4):
                ws.column_dimensions[chr(64 + col)].width = 20

            # حفظ الملف
            wb.save(filename)
            messagebox.showinfo("تم", f"تم تصدير التقرير بنجاح إلى:\n{filename}")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تصدير الملف: {str(e)}")

def main():
    """الدالة الرئيسية لتشغيل البرنامج"""
    root = tk.Tk()
    app = PromotionCalculator(root)

    # تعيين الخط العربي إذا كان متاحاً
    try:
        root.option_add('*Font', 'Arial 10')
    except:
        pass

    root.mainloop()

if __name__ == "__main__":
    main()
