#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج حساب تسلسل الترقيات
Promotion Sequence Calculator
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from tkcalendar import DateEntry
import json
import os
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment
import sys

class PromotionCalculator:
    def __init__(self, root):
        self.root = root
        self.root.title("برنامج حساب تسلسل الترقيات")
        self.root.geometry("800x600")
        self.root.configure(bg='#f0f0f0')
        
        # تحديد مسار ملف الإعدادات
        if getattr(sys, 'frozen', False):
            # إذا كان البرنامج مجمد (exe)
            self.app_dir = os.path.dirname(sys.executable)
        else:
            # إذا كان البرنامج يعمل من Python
            self.app_dir = os.path.dirname(os.path.abspath(__file__))
        
        self.settings_file = os.path.join(self.app_dir, "promotion_settings.json")
        
        # بيانات افتراضية للترقيات (حتى الدرجة السادسة عشر)
        self.promotion_periods = {
            "الدرجة الأولى": {"years": 4, "months": 0},
            "الدرجة الثانية": {"years": 4, "months": 0},
            "الدرجة الثالثة": {"years": 4, "months": 0},
            "الدرجة الرابعة": {"years": 4, "months": 0},
            "الدرجة الخامسة": {"years": 4, "months": 0},
            "الدرجة السادسة": {"years": 4, "months": 0},
            "الدرجة السابعة": {"years": 4, "months": 0},
            "الدرجة الثامنة": {"years": 4, "months": 0},
            "الدرجة التاسعة": {"years": 4, "months": 0},
            "الدرجة العاشرة": {"years": 4, "months": 0},
            "الدرجة الحادية عشر": {"years": 4, "months": 0},
            "الدرجة الثانية عشر": {"years": 4, "months": 0},
            "الدرجة الثالثة عشر": {"years": 4, "months": 0},
            "الدرجة الرابعة عشر": {"years": 4, "months": 0},
            "الدرجة الخامسة عشر": {"years": 4, "months": 0},
            "الدرجة السادسة عشر": {"years": 4, "months": 0}
        }

        # قائمة الدرجات المرتبة
        self.degree_order = list(self.promotion_periods.keys())
        
        self.load_settings()
        self.create_widgets()
        
    def load_settings(self):
        """تحميل إعدادات مدد الترقيات من الملف"""
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if isinstance(data, dict) and 'promotion_periods' in data:
                        self.promotion_periods = data['promotion_periods']
                        self.degree_order = data.get('degree_order', list(self.promotion_periods.keys()))
                    else:
                        # للتوافق مع الإصدارات القديمة
                        self.promotion_periods = data
                        self.degree_order = list(self.promotion_periods.keys())
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل الإعدادات: {str(e)}")

    def save_settings(self):
        """حفظ إعدادات مدد الترقيات في الملف"""
        try:
            data = {
                'promotion_periods': self.promotion_periods,
                'degree_order': self.degree_order
            }
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ الإعدادات: {str(e)}")
    
    def create_widgets(self):
        """إنشاء واجهة المستخدم"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # إعداد الشبكة
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # عنوان البرنامج
        title_label = ttk.Label(main_frame, text="برنامج حساب تسلسل الترقيات", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # إدخال اسم الموظف
        ttk.Label(main_frame, text="اسم الموظف:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.employee_name = ttk.Entry(main_frame, width=30)
        self.employee_name.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 0))
        
        # إدخال تاريخ المباشرة
        ttk.Label(main_frame, text="تاريخ أول مباشرة:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.start_date = DateEntry(main_frame, width=12, background='darkblue',
                                   foreground='white', borderwidth=2,
                                   date_pattern='dd/mm/yyyy')
        self.start_date.grid(row=2, column=1, sticky=tk.W, pady=5, padx=(5, 0))

        # إدخال الدرجة عند التعيين
        ttk.Label(main_frame, text="الدرجة عند التعيين:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.starting_degree = ttk.Combobox(main_frame, width=27, state="readonly")
        self.starting_degree.grid(row=3, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 0))
        self.update_degree_combobox()

        # إدخال عدد العلاوات
        ttk.Label(main_frame, text="عدد العلاوات (السنوات المقضية):").grid(row=4, column=0, sticky=tk.W, pady=5)
        allowances_frame = ttk.Frame(main_frame)
        allowances_frame.grid(row=4, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 0))

        self.allowances_count = tk.StringVar(value="0")
        allowances_spinbox = ttk.Spinbox(allowances_frame, from_=0, to=50, width=10,
                                        textvariable=self.allowances_count)
        allowances_spinbox.pack(side=tk.LEFT)

        ttk.Label(allowances_frame, text="علاوة (سنة لكل علاوة)",
                 foreground='gray').pack(side=tk.LEFT, padx=(10, 0))
        
        # أزرار التحكم
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=5, column=0, columnspan=3, pady=20)
        
        ttk.Button(button_frame, text="إعداد مدد الترقيات", 
                  command=self.open_settings_window).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="حساب الترقيات", 
                  command=self.calculate_promotions).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="تصدير Excel", 
                  command=self.export_to_excel).pack(side=tk.LEFT, padx=5)
        
        # جدول النتائج
        self.create_results_table(main_frame)

    def update_degree_combobox(self):
        """تحديث قائمة الدرجات في ComboBox"""
        self.starting_degree['values'] = self.degree_order
        if self.degree_order:
            self.starting_degree.set(self.degree_order[0])  # تعيين الدرجة الأولى كافتراضية
        
    def create_results_table(self, parent):
        """إنشاء جدول عرض النتائج"""
        # إطار الجدول
        table_frame = ttk.LabelFrame(parent, text="نتائج حساب الترقيات", padding="10")
        table_frame.grid(row=6, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        table_frame.columnconfigure(0, weight=1)
        table_frame.rowconfigure(0, weight=1)
        
        # إنشاء Treeview
        columns = ('degree', 'promotion_date', 'status')
        self.results_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # تحديد عناوين الأعمدة
        self.results_tree.heading('degree', text='الدرجة')
        self.results_tree.heading('promotion_date', text='تاريخ الاستحقاق')
        self.results_tree.heading('status', text='الحالة')
        
        # تحديد عرض الأعمدة
        self.results_tree.column('degree', width=150, anchor='center')
        self.results_tree.column('promotion_date', width=150, anchor='center')
        self.results_tree.column('status', width=100, anchor='center')
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.results_tree.yview)
        self.results_tree.configure(yscrollcommand=scrollbar.set)
        
        # وضع الجدول وشريط التمرير
        self.results_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # متغير لحفظ النتائج
        self.promotion_results = []
    
    def open_settings_window(self):
        """فتح نافذة إعداد مدد الترقيات"""
        settings_window = tk.Toplevel(self.root)
        settings_window.title("إعداد مدد الترقيات")
        settings_window.geometry("500x400")
        settings_window.configure(bg='#f0f0f0')
        
        # إطار رئيسي
        main_frame = ttk.Frame(settings_window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # عنوان
        ttk.Label(main_frame, text="تحديد مدد المكوث لكل درجة", 
                 font=('Arial', 12, 'bold')).pack(pady=(0, 10))
        
        # إطار الجدول
        table_frame = ttk.Frame(main_frame)
        table_frame.pack(fill=tk.BOTH, expand=True)
        
        # إنشاء جدول الإعدادات
        columns = ('degree', 'years', 'months')
        settings_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=10)
        
        settings_tree.heading('degree', text='الدرجة')
        settings_tree.heading('years', text='السنوات')
        settings_tree.heading('months', text='الأشهر')
        
        settings_tree.column('degree', width=200, anchor='center')
        settings_tree.column('years', width=100, anchor='center')
        settings_tree.column('months', width=100, anchor='center')
        
        # ملء الجدول بالبيانات الحالية حسب الترتيب
        for degree in self.degree_order:
            if degree in self.promotion_periods:
                period = self.promotion_periods[degree]
                settings_tree.insert('', tk.END, values=(degree, period['years'], period['months']))
        
        settings_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # شريط التمرير
        scrollbar_settings = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=settings_tree.yview)
        settings_tree.configure(yscrollcommand=scrollbar_settings.set)
        scrollbar_settings.pack(side=tk.RIGHT, fill=tk.Y)
        
        # إطار التحرير
        edit_frame = ttk.LabelFrame(main_frame, text="تحرير المدة", padding="10")
        edit_frame.pack(fill=tk.X, pady=10)
        
        ttk.Label(edit_frame, text="السنوات:").grid(row=0, column=0, padx=5)
        years_var = tk.StringVar()
        years_entry = ttk.Entry(edit_frame, textvariable=years_var, width=10)
        years_entry.grid(row=0, column=1, padx=5)
        
        ttk.Label(edit_frame, text="الأشهر:").grid(row=0, column=2, padx=5)
        months_var = tk.StringVar()
        months_entry = ttk.Entry(edit_frame, textvariable=months_var, width=10)
        months_entry.grid(row=0, column=3, padx=5)
        
        def on_select(event):
            selection = settings_tree.selection()
            if selection:
                item = settings_tree.item(selection[0])
                degree = item['values'][0]
                years_var.set(str(self.promotion_periods[degree]['years']))
                months_var.set(str(self.promotion_periods[degree]['months']))
        
        settings_tree.bind('<<TreeviewSelect>>', on_select)
        
        def update_period():
            selection = settings_tree.selection()
            if selection:
                try:
                    years = int(years_var.get()) if years_var.get() else 0
                    months = int(months_var.get()) if months_var.get() else 0
                    
                    item = settings_tree.item(selection[0])
                    degree = item['values'][0]
                    
                    self.promotion_periods[degree]['years'] = years
                    self.promotion_periods[degree]['months'] = months
                    
                    # تحديث الجدول
                    settings_tree.item(selection[0], values=(degree, years, months))
                    
                    messagebox.showinfo("تم", "تم تحديث المدة بنجاح")
                except ValueError:
                    messagebox.showerror("خطأ", "يرجى إدخال أرقام صحيحة")
            else:
                messagebox.showwarning("تحذير", "يرجى اختيار درجة من الجدول")
        
        ttk.Button(edit_frame, text="تحديث", command=update_period).grid(row=0, column=4, padx=10)
        
        # إطار إضافة درجة جديدة
        add_frame = ttk.LabelFrame(main_frame, text="إضافة درجة جديدة", padding="10")
        add_frame.pack(fill=tk.X, pady=10)

        ttk.Label(add_frame, text="اسم الدرجة:").grid(row=0, column=0, padx=5)
        new_degree_var = tk.StringVar()
        new_degree_entry = ttk.Entry(add_frame, textvariable=new_degree_var, width=20)
        new_degree_entry.grid(row=0, column=1, padx=5)

        ttk.Label(add_frame, text="السنوات:").grid(row=0, column=2, padx=5)
        new_years_var = tk.StringVar(value="4")
        new_years_entry = ttk.Entry(add_frame, textvariable=new_years_var, width=10)
        new_years_entry.grid(row=0, column=3, padx=5)

        ttk.Label(add_frame, text="الأشهر:").grid(row=0, column=4, padx=5)
        new_months_var = tk.StringVar(value="0")
        new_months_entry = ttk.Entry(add_frame, textvariable=new_months_var, width=10)
        new_months_entry.grid(row=0, column=5, padx=5)

        def add_new_degree():
            degree_name = new_degree_var.get().strip()
            if not degree_name:
                messagebox.showwarning("تحذير", "يرجى إدخال اسم الدرجة")
                return

            if degree_name in self.promotion_periods:
                messagebox.showwarning("تحذير", "هذه الدرجة موجودة مسبقاً")
                return

            try:
                years = int(new_years_var.get()) if new_years_var.get() else 0
                months = int(new_months_var.get()) if new_months_var.get() else 0

                self.promotion_periods[degree_name] = {"years": years, "months": months}
                self.degree_order.append(degree_name)

                # إضافة للجدول
                settings_tree.insert('', tk.END, values=(degree_name, years, months))

                # مسح الحقول
                new_degree_var.set("")
                new_years_var.set("4")
                new_months_var.set("0")

                messagebox.showinfo("تم", "تم إضافة الدرجة بنجاح")

            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال أرقام صحيحة للسنوات والأشهر")

        def delete_degree():
            selection = settings_tree.selection()
            if not selection:
                messagebox.showwarning("تحذير", "يرجى اختيار درجة لحذفها")
                return

            item = settings_tree.item(selection[0])
            degree_name = item['values'][0]

            if len(self.promotion_periods) <= 1:
                messagebox.showwarning("تحذير", "لا يمكن حذف جميع الدرجات")
                return

            result = messagebox.askyesno("تأكيد الحذف", f"هل تريد حذف {degree_name}؟")
            if result:
                del self.promotion_periods[degree_name]
                self.degree_order.remove(degree_name)
                settings_tree.delete(selection[0])
                messagebox.showinfo("تم", "تم حذف الدرجة بنجاح")

        ttk.Button(add_frame, text="إضافة درجة", command=add_new_degree).grid(row=0, column=6, padx=10)

        # أزرار إعادة الترتيب والحذف
        order_frame = ttk.Frame(add_frame)
        order_frame.grid(row=1, column=0, columnspan=7, pady=5)

        def move_up():
            selection = settings_tree.selection()
            if not selection:
                messagebox.showwarning("تحذير", "يرجى اختيار درجة لرفعها")
                return

            item = settings_tree.item(selection[0])
            degree_name = item['values'][0]

            try:
                current_index = self.degree_order.index(degree_name)
                if current_index > 0:
                    # تبديل المواضع
                    self.degree_order[current_index], self.degree_order[current_index - 1] = \
                        self.degree_order[current_index - 1], self.degree_order[current_index]

                    # إعادة ملء الجدول
                    refresh_table()
                    messagebox.showinfo("تم", "تم رفع الدرجة بنجاح")
                else:
                    messagebox.showinfo("تنبيه", "الدرجة في أعلى القائمة بالفعل")
            except ValueError:
                messagebox.showerror("خطأ", "خطأ في العثور على الدرجة")

        def move_down():
            selection = settings_tree.selection()
            if not selection:
                messagebox.showwarning("تحذير", "يرجى اختيار درجة لخفضها")
                return

            item = settings_tree.item(selection[0])
            degree_name = item['values'][0]

            try:
                current_index = self.degree_order.index(degree_name)
                if current_index < len(self.degree_order) - 1:
                    # تبديل المواضع
                    self.degree_order[current_index], self.degree_order[current_index + 1] = \
                        self.degree_order[current_index + 1], self.degree_order[current_index]

                    # إعادة ملء الجدول
                    refresh_table()
                    messagebox.showinfo("تم", "تم خفض الدرجة بنجاح")
                else:
                    messagebox.showinfo("تنبيه", "الدرجة في أسفل القائمة بالفعل")
            except ValueError:
                messagebox.showerror("خطأ", "خطأ في العثور على الدرجة")

        def refresh_table():
            """إعادة ملء الجدول حسب الترتيب الجديد"""
            # مسح الجدول
            for item in settings_tree.get_children():
                settings_tree.delete(item)

            # إعادة ملء الجدول
            for degree in self.degree_order:
                if degree in self.promotion_periods:
                    period = self.promotion_periods[degree]
                    settings_tree.insert('', tk.END, values=(degree, period['years'], period['months']))

        ttk.Button(order_frame, text="رفع ↑", command=move_up).pack(side=tk.LEFT, padx=5)
        ttk.Button(order_frame, text="خفض ↓", command=move_down).pack(side=tk.LEFT, padx=5)
        ttk.Button(order_frame, text="حذف الدرجة المختارة", command=delete_degree).pack(side=tk.LEFT, padx=5)

        # أزرار الحفظ والإلغاء
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=10)

        def save_and_close():
            self.save_settings()
            self.update_degree_combobox()  # تحديث قائمة الدرجات في النافذة الرئيسية
            settings_window.destroy()
            messagebox.showinfo("تم", "تم حفظ الإعدادات بنجاح")

        ttk.Button(button_frame, text="حفظ وإغلاق", command=save_and_close).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="إلغاء", command=settings_window.destroy).pack(side=tk.LEFT, padx=5)

    def calculate_promotions(self):
        """حساب تسلسل الترقيات"""
        try:
            # التحقق من صحة البيانات
            start_date = self.start_date.get_date()
            employee_name = self.employee_name.get().strip()
            starting_degree = self.starting_degree.get()
            allowances = int(self.allowances_count.get()) if self.allowances_count.get() else 0

            if not employee_name:
                employee_name = "غير محدد"

            if not starting_degree:
                messagebox.showwarning("تحذير", "يرجى اختيار الدرجة عند التعيين")
                return

            # مسح النتائج السابقة
            for item in self.results_tree.get_children():
                self.results_tree.delete(item)

            self.promotion_results = []
            current_date = start_date
            today = datetime.now().date()

            # العثور على فهرس الدرجة المختارة
            try:
                start_index = self.degree_order.index(starting_degree)
            except ValueError:
                messagebox.showerror("خطأ", "الدرجة المختارة غير موجودة")
                return

            # إضافة الدرجة الحالية (عند التعيين) مع العلاوات
            allowances_text = f" + {allowances} علاوة" if allowances > 0 else ""
            current_degree_result = {
                'degree': f"{starting_degree}{allowances_text} (الحالية)",
                'promotion_date': start_date.strftime('%d/%m/%Y'),
                'status': f"درجة حالية{allowances_text}",
                'date_obj': start_date
            }
            self.promotion_results.append(current_degree_result)
            self.results_tree.insert('', tk.END,
                                   values=(f"{starting_degree}{allowances_text} (الحالية)",
                                          start_date.strftime('%d/%m/%Y'),
                                          f"درجة حالية{allowances_text}"),
                                   tags=('current',))

            # حساب تاريخ الترقية التالية بناءً على العلاوات
            current_degree_period = self.promotion_periods[starting_degree]

            # المدة المتبقية = مدة الدرجة - العلاوات المقضية
            remaining_years = current_degree_period['years'] - allowances
            remaining_months = current_degree_period['months']

            # تاريخ الترقية التالية = تاريخ المباشرة + العلاوات + المدة المتبقية
            # أو بشكل مبسط: تاريخ المباشرة + المدة المتبقية فقط
            if remaining_years <= 0 and remaining_months <= 0:
                # إذا كانت العلاوات أكبر من أو تساوي مدة الدرجة، فالموظف مستحق للترقية فوراً
                next_promotion_date = start_date
            else:
                # تاريخ الترقية = تاريخ المباشرة + المدة المتبقية
                next_promotion_date = start_date + relativedelta(years=remaining_years, months=remaining_months)

            current_date = next_promotion_date

            # حساب جميع الترقيات التالية
            for i in range(start_index + 1, len(self.degree_order)):
                degree = self.degree_order[i]

                # تحديد حالة الترقية
                if current_date <= today:
                    status = "مستحقة"
                    tag = "completed"
                elif current_date > today:
                    # أول ترقية قادمة
                    if not any(result['status'] == 'قادمة' for result in self.promotion_results):
                        status = "قادمة"
                        tag = "upcoming"
                    else:
                        status = "مستقبلية"
                        tag = "future"
                else:
                    status = "مستقبلية"
                    tag = "future"

                result = {
                    'degree': degree,
                    'promotion_date': current_date.strftime('%d/%m/%Y'),
                    'status': status,
                    'date_obj': current_date
                }
                self.promotion_results.append(result)
                self.results_tree.insert('', tk.END,
                                       values=(degree, current_date.strftime('%d/%m/%Y'), status),
                                       tags=(tag,))

                # حساب تاريخ الترقية التالية
                if i < len(self.degree_order) - 1:
                    period = self.promotion_periods[degree]
                    current_date = current_date + relativedelta(years=period['years'], months=period['months'])

            # تنسيق الألوان
            self.results_tree.tag_configure('current', background='#e3f2fd', foreground='#1565c0')
            self.results_tree.tag_configure('completed', background='#d4edda')
            self.results_tree.tag_configure('upcoming', background='#fff3cd', foreground='#856404')
            self.results_tree.tag_configure('future', background='#f8f9fa')

            # عرض رسالة النجاح
            next_promotion = next((r for r in self.promotion_results if r['status'] == 'قادمة'), None)
            allowances_info = f"\nعدد العلاوات المقضية: {allowances} سنة" if allowances > 0 else ""

            if next_promotion:
                messagebox.showinfo("تم الحساب",
                                  f"تم حساب الترقيات بنجاح{allowances_info}\nالترقية القادمة: {next_promotion['degree']} في {next_promotion['promotion_date']}")
            else:
                messagebox.showinfo("تم الحساب", f"تم حساب الترقيات بنجاح{allowances_info}")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حساب الترقيات: {str(e)}")

    def export_to_excel(self):
        """تصدير النتائج إلى ملف Excel"""
        if not self.promotion_results:
            messagebox.showwarning("تحذير", "لا توجد نتائج للتصدير. يرجى حساب الترقيات أولاً")
            return

        try:
            # اختيار مكان الحفظ
            filename = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                title="حفظ تقرير الترقيات"
            )

            if not filename:
                return

            # إنشاء ملف Excel
            wb = Workbook()
            ws = wb.active
            ws.title = "تقرير الترقيات"

            # معلومات الموظف
            employee_name = self.employee_name.get().strip() or "غير محدد"
            start_date = self.start_date.get_date().strftime('%d/%m/%Y')
            starting_degree = self.starting_degree.get()
            allowances = int(self.allowances_count.get()) if self.allowances_count.get() else 0

            ws['A1'] = "تقرير تسلسل الترقيات"
            ws['A1'].font = Font(size=16, bold=True)
            ws['A2'] = f"اسم الموظف: {employee_name}"
            ws['A3'] = f"تاريخ أول مباشرة: {start_date}"
            ws['A4'] = f"الدرجة عند التعيين: {starting_degree}"
            ws['A5'] = f"عدد العلاوات: {allowances} سنة"
            ws['A6'] = f"تاريخ إعداد التقرير: {datetime.now().strftime('%d/%m/%Y %H:%M')}"

            # عناوين الجدول
            headers = ['الدرجة', 'تاريخ الاستحقاق', 'الحالة']
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=8, column=col, value=header)
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
                cell.alignment = Alignment(horizontal="center")

            # بيانات الترقيات
            for row, result in enumerate(self.promotion_results, 9):
                ws.cell(row=row, column=1, value=result['degree'])
                ws.cell(row=row, column=2, value=result['promotion_date'])
                ws.cell(row=row, column=3, value=result['status'])

                # تلوين الصفوف حسب الحالة
                if result['status'] == 'قادمة':
                    for col in range(1, 4):
                        ws.cell(row=row, column=col).fill = PatternFill(
                            start_color="FFF3CD", end_color="FFF3CD", fill_type="solid")
                elif result['status'] == 'مستحقة':
                    for col in range(1, 4):
                        ws.cell(row=row, column=col).fill = PatternFill(
                            start_color="D4EDDA", end_color="D4EDDA", fill_type="solid")

            # تنسيق العرض
            for col in range(1, 4):
                ws.column_dimensions[chr(64 + col)].width = 20

            # حفظ الملف
            wb.save(filename)
            messagebox.showinfo("تم", f"تم تصدير التقرير بنجاح إلى:\n{filename}")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تصدير الملف: {str(e)}")

def main():
    """الدالة الرئيسية لتشغيل البرنامج"""
    root = tk.Tk()
    app = PromotionCalculator(root)

    # تعيين الخط العربي إذا كان متاحاً
    try:
        root.option_add('*Font', 'Arial 10')
    except:
        pass

    root.mainloop()

if __name__ == "__main__":
    main()
